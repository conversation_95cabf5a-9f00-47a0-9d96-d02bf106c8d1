// src/App.tsx
import React, { useState, useEffect } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
  TouchableOpacity,
} from 'react-native';
import { QuickChoicesBar } from './components/QuickChoicesBar';
import { DualQuickChoicesBar } from './components/DualQuickChoicesBar';

const EXCHANGE_RATE = 3.43; // 1 EUR = 3.43 TND

const App: React.FC = () => {
  const [number, setNumber] = useState<string>('');
  const [currentMode, setCurrentMode] = useState<'simple' | 'converter'>('simple');
  const [euroAmount, setEuroAmount] = useState<string>('');
  const [dinarAmount, setDinarAmount] = useState<string>('');
  const [isKeyboardVisible, setIsKeyboardVisible] = useState<boolean>(false);
  const [activeInput, setActiveInput] = useState<'euro' | 'dinar' | 'simple' | null>(null);

  const quickChoices = [10, 20, 30, 50, 100];
  const euroQuickChoices = [10, 20, 50, 100, 200];
  const dinarQuickChoices = [34, 69, 172, 343, 686];

  // Gestion clavier
  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => setIsKeyboardVisible(true));
    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setIsKeyboardVisible(false);
      setActiveInput(null);
    });
    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  // Mode simple
  const handleQuickChoice = (value: number) => {
    setNumber(value.toString());
    Keyboard.dismiss();
  };

  const handleSimpleInputFocus = () => setActiveInput('simple');
  const handleTextChange = (text: string) => setNumber(text.replace(/[^0-9]/g, ''));

  // Convertisseur Euro/Dinar
  const handleEuroChange = (text: string) => {
    const formattedText = text.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
    setEuroAmount(formattedText);
    const euroValue = parseFloat(formattedText);
    setDinarAmount(!isNaN(euroValue) ? (euroValue * EXCHANGE_RATE).toFixed(2) : '');
  };

  const handleDinarChange = (text: string) => {
    const formattedText = text.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
    setDinarAmount(formattedText);
    const dinarValue = parseFloat(formattedText);
    setEuroAmount(!isNaN(dinarValue) ? (dinarValue / EXCHANGE_RATE).toFixed(2) : '');
  };

  const handleEuroQuickChoice = (value: number) => {
    setEuroAmount(value.toString());
    setDinarAmount((value * EXCHANGE_RATE).toFixed(2));
    Keyboard.dismiss();
  };

  const handleDinarQuickChoice = (value: number) => {
    setDinarAmount(value.toString());
    setEuroAmount((value / EXCHANGE_RATE).toFixed(2));
    Keyboard.dismiss();
  };

  const toggleMode = () => {
    setCurrentMode(currentMode === 'simple' ? 'converter' : 'simple');
    setNumber('');
    setEuroAmount('');
    setDinarAmount('');
    setActiveInput(null);
    Keyboard.dismiss();
  };

  const renderContent = () => currentMode === 'simple' ? (
    <View style={styles.content}>
      <Text style={styles.title}>Saisie de Nombre</Text>
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          value={number}
          onChangeText={handleTextChange}
          onFocus={handleSimpleInputFocus}
          placeholder="Entrez un nombre"
          keyboardType="number-pad"
        />
      </View>
      {number && <Text style={styles.displayValue}>Valeur saisie : {number}</Text>}
    </View>
  ) : (
    <View style={styles.content}>
      <Text style={styles.title}>Convertisseur Euro/Dinar</Text>
      <Text style={styles.exchangeRate}>Taux de change : 1 EUR = {EXCHANGE_RATE} TND</Text>
      <View style={styles.inputContainer}>
        <Text style={styles.currencyLabel}>Euro (EUR)</Text>
        <TextInput
          style={[styles.input, activeInput === 'euro' && styles.inputFocused]}
          value={euroAmount}
          onChangeText={handleEuroChange}
          onFocus={() => setActiveInput('euro')}
          placeholder="Montant en Euro"
          keyboardType="decimal-pad"
        />
      </View>
      <View style={styles.conversionIndicator}><Text style={styles.arrowText}>⇅</Text></View>
      <View style={styles.inputContainer}>
        <Text style={styles.currencyLabel}>Dinar Tunisien (TND)</Text>
        <TextInput
          style={[styles.input, activeInput === 'dinar' && styles.inputFocused]}
          value={dinarAmount}
          onChangeText={handleDinarChange}
          onFocus={() => setActiveInput('dinar')}
          placeholder="Montant en Dinar"
          keyboardType="decimal-pad"
        />
      </View>
      {(euroAmount || dinarAmount) && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultText}>{euroAmount} EUR = {dinarAmount} TND</Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView style={styles.keyboardAvoidingView} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
          <View style={styles.toggleContainer}>
            <TouchableOpacity style={styles.toggleButton} onPress={toggleMode}>
              <Text style={styles.toggleText}>{currentMode === 'simple' ? '📱 → 💱' : '💱 → 📱'}</Text>
              <Text style={styles.toggleLabel}>{currentMode === 'simple' ? 'Convertisseur' : 'Saisie simple'}</Text>
            </TouchableOpacity>
          </View>
          {renderContent()}
          {currentMode === 'simple' ? (
            <QuickChoicesBar
              choices={quickChoices}
              selectedValue={number}
              onChoiceSelect={handleQuickChoice}
              isVisible={isKeyboardVisible && activeInput === 'simple'}
            />
          ) : (
            <DualQuickChoicesBar
              euroChoices={euroQuickChoices}
              dinarChoices={dinarQuickChoices}
              activeInput={activeInput === 'euro' || activeInput === 'dinar' ? activeInput : null}
              onEuroChoiceSelect={handleEuroQuickChoice}
              onDinarChoiceSelect={handleDinarQuickChoice}
              isVisible={isKeyboardVisible && (activeInput === 'euro' || activeInput === 'dinar')}
            />
          )}
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f5f5f5' },
  keyboardAvoidingView: { flex: 1 },
  toggleContainer: { padding: 20, alignItems: 'center' },
  toggleButton: { backgroundColor: '#007AFF', padding: 10, borderRadius: 20, flexDirection: 'row', alignItems: 'center' },
  toggleText: { fontSize: 16, marginRight: 8 },
  toggleLabel: { fontSize: 14, fontWeight: '600', color: '#fff' },
  content: { flex: 1, padding: 20, justifyContent: 'center' },
  title: { fontSize: 24, fontWeight: 'bold', textAlign: 'center', marginBottom: 10, color: '#333' },
  exchangeRate: { fontSize: 16, textAlign: 'center', marginBottom: 30, color: '#666', fontStyle: 'italic' },
  inputContainer: { marginBottom: 15 },
  currencyLabel: { fontSize: 16, fontWeight: '600', marginBottom: 8, color: '#333' },
  input: { backgroundColor: '#fff', borderWidth: 2, borderColor: '#ddd', borderRadius: 10, padding: 15, fontSize: 18, textAlign: 'center' },
  inputFocused: { borderColor: '#007AFF' },
  conversionIndicator: { alignItems: 'center', marginVertical: 10 },
  arrowText: { fontSize: 24, color: '#007AFF', fontWeight: 'bold' },
  resultContainer: { backgroundColor: '#e8f4fd', padding: 15, borderRadius: 10, marginTop: 20 },
  resultText: { fontSize: 16, textAlign: 'center', color: '#007AFF', fontWeight: '600' },
  displayValue: { fontSize: 18, textAlign: 'center', marginTop: 10, color: '#444' },
});

export default App;
