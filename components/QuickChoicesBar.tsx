// src/components/QuickChoicesBar.tsx
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface QuickChoicesBarProps {
  choices: number[];
  selectedValue: string;
  onChoiceSelect: (value: number) => void;
  isVisible: boolean;
}

export const QuickChoicesBar: React.FC<QuickChoicesBarProps> = ({
  choices, selectedValue, onChoiceSelect, isVisible
}) => {
  if (!isVisible) return null;
  return (
    <View style={styles.container}>
      {choices.map((choice) => (
        <TouchableOpacity
          key={choice}
          style={[styles.choice, selectedValue === choice.toString() && styles.choiceSelected]}
          onPress={() => onChoiceSelect(choice)}
        >
          <Text style={styles.choiceText}>{choice}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flexDirection: 'row', justifyContent: 'space-around', padding: 15, backgroundColor: '#fff' },
  choice: { backgroundColor: '#007AFF', padding: 10, borderRadius: 8, minWidth: 50, alignItems: 'center' },
  choiceSelected: { backgroundColor: '#005BBB' },
  choiceText: { color: '#fff', fontWeight: '600' },
});
