// src/components/DualQuickChoicesBar.tsx
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface DualQuickChoicesBarProps {
  euroChoices: number[];
  dinarChoices: number[];
  activeInput: 'euro' | 'dinar' | null;
  onEuroChoiceSelect: (value: number) => void;
  onDinarChoiceSelect: (value: number) => void;
  isVisible: boolean;
}

export const DualQuickChoicesBar: React.FC<DualQuickChoicesBarProps> = ({
  euroChoices, dinarChoices, activeInput, onEuroChoiceSelect, onDinarChoiceSelect, isVisible
}) => {
  if (!isVisible || !activeInput) return null;
  const choices = activeInput === 'euro' ? euroChoices : dinarChoices;
  const onChoiceSelect = activeInput === 'euro' ? onEuroChoiceSelect : onDinarChoiceSelect;
  const currency = activeInput === 'euro' ? 'EUR' : 'TND';

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Choix rapides ({currency}) :</Text>
      <View style={styles.row}>
        {choices.map((choice) => (
          <TouchableOpacity key={choice} style={styles.button} onPress={() => onChoiceSelect(choice)}>
            <Text style={styles.text}>{choice}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { backgroundColor: '#fff', padding: 15, borderTopWidth: 1, borderTopColor: '#e0e0e0' },
  title: { fontSize: 14, fontWeight: '600', color: '#333', marginBottom: 10, textAlign: 'center' },
  row: { flexDirection: 'row', justifyContent: 'space-around' },
  button: { backgroundColor: '#007AFF', padding: 10, borderRadius: 8, minWidth: 50, alignItems: 'center' },
  text: { color: '#fff', fontWeight: '600' },
});
